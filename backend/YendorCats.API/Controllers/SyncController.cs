using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for metadata synchronization operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class SyncController : ControllerBase
    {
        private readonly ILogger<SyncController> _logger;
        private readonly IMetadataSyncService _metadataSyncService;

        public SyncController(
            ILogger<SyncController> logger,
            IMetadataSyncService metadataSyncService)
        {
            _logger = logger;
            _metadataSyncService = metadataSyncService;
        }

        /// <summary>
        /// Trigger immediate metadata synchronization from B2 bucket
        /// </summary>
        /// <param name="category">Optional specific category to sync (studs, queens, kittens, gallery)</param>
        /// <returns>Sync result with statistics</returns>
        [HttpPost("metadata")]
        [ProducesResponseType(StatusCodes.Status200OK)]
